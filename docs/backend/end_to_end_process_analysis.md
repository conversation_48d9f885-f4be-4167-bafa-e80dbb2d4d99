# 🔄 Complete End-to-End Process Analysis: Frontend ↔ Backend

## 📋 **Executive Summary**

This document provides a comprehensive analysis of all major user flows in the Hopen application, from Flutter frontend to Go backend and back. The analysis covers authentication, profile management, media upload, contact search, requests, real-time communication, and more.

## 🏗️ **System Architecture Overview**

### **Frontend Architecture (Flutter)**
```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
├─────────────────────────────────────────────────────────────┤
│ Pages → Widgets → BLoCs (State Management)                  │
│ • LoginPage → AuthBloc                                      │
│ • ContactsPage → ContactsBloc                               │
│ • ProfilePage → UserProfileBloc                             │
│ • BubblePage → BubbleBloc                                   │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   BUSINESS LOGIC LAYER                      │
├─────────────────────────────────────────────────────────────┤
│ BLoCs → Use Cases → Repositories                            │
│ • AuthBloc → LoginUseCase → AuthRepository                  │
│ • ContactsBloc → SearchContactsUseCase → ContactsRepository │
│ • UserProfileBloc → UpdateProfileUseCase → UserRepository   │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                      DATA LAYER                             │
├─────────────────────────────────────────────────────────────┤
│ Repositories → Data Sources → API Services                  │
│ • AuthRepository → HttpRemoteDataSource → HttpApiService    │
│ • ContactsRepository → ContactsRemoteDataSource → API       │
│ • UserRepository → HttpRemoteDataSource → HTTP3Client       │
└─────────────────────────────────────────────────────────────┘
```

### **Backend Architecture (Go Microservices)**
```
┌─────────────────────────────────────────────────────────────┐
│                    API GATEWAY LAYER                        │
├─────────────────────────────────────────────────────────────┤
│ Gin Router → Middleware → Service Routes                    │
│ • /api/v1/auth/* → AuthService                              │
│ • /api/v1/users/* → UserService                             │
│ • /api/v1/contact/* → ContactService                        │
│ • /api/v1/media/* → MediaService                            │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   MICROSERVICES LAYER                       │
├─────────────────────────────────────────────────────────────┤
│ 12 Core Services:                                           │
│ • 🔐 auth: JWT + OAuth + MQTT auth                          │
│ • 👤 user: Profile management + search                      │
│ • 🤝 contact: Contact relationships + requests              │
│ • 🫧 bubble: Bubble lifecycle + membership                  │
│ • 📱 media: File upload/download + MinIO                    │
│ • 📞 call: WebRTC + signaling                               │
│ • 💬 chat: Real-time messaging                              │
│ • 🔔 notification: FCM + push notifications                 │
│ • 👥 friendship: Auto-generated friendships                 │
│ • 📊 analytics: Event tracking + insights                   │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    INFRASTRUCTURE LAYER                     │
├─────────────────────────────────────────────────────────────┤
│ • PostgreSQL: User data + relationships + bubbles          │
│ • Cassandra: Chat messages + real-time data                │
│ • MinIO: File storage + profile pictures                   │
│ • EMQX: MQTT broker + real-time messaging                  │
│ • Valkey: Caching + session storage                        │
│ • NATS: Event streaming + microservice communication       │
│ • Kratos: Identity management + authentication             │
│ • Hydra: OAuth2 server + authorization                     │
└─────────────────────────────────────────────────────────────┘
```

## 🔐 **1. Authentication Flow (Sign In/Sign Up)**

### **Frontend → Backend Flow:**

#### **1.1 Sign Up Process**
```mermaid
sequenceDiagram
    participant U as User
    participant LP as LoginPage
    participant AB as AuthBloc
    participant AR as AuthRepository
    participant OAS as OryAuthService
    participant K as Kratos
    participant BE as Backend

    U->>LP: Enter credentials
    LP->>AB: SignUpEvent
    AB->>AR: signUp()
    AR->>OAS: signUpWithEmail()
    OAS->>K: POST /self-service/registration
    K-->>OAS: Registration flow
    OAS->>BE: POST /api/v1/auth/register
    BE-->>OAS: User created
    OAS-->>AR: AuthResponse
    AR-->>AB: Result<UserModel>
    AB-->>LP: AuthAuthenticated
    LP->>U: Navigate to home
```

#### **1.2 Sign In Process**
```mermaid
sequenceDiagram
    participant U as User
    participant LP as LoginPage
    participant AB as AuthBloc
    participant AR as AuthRepository
    participant OAS as OryAuthService
    participant K as Kratos

    U->>LP: Enter email/password
    LP->>AB: LoginEvent
    AB->>AR: login()
    AR->>OAS: signInWithEmail()
    OAS->>K: POST /self-service/login
    K-->>OAS: Session token (ory_st_xxx)
    OAS-->>AR: AuthResponse with user
    AR-->>AB: Result<UserModel>
    AB-->>LP: AuthAuthenticated
    LP->>U: Navigate to home
```

#### **1.3 Session Validation**
- **Frontend**: Stores session token securely
- **Backend**: Validates `Authorization: Bearer ory_st_xxx` via Kratos
- **Middleware**: `AuthMiddleware` validates every API request
- **MQTT**: Uses same session token for real-time authentication

## 📸 **2. Profile Picture Upload Flow**

### **Frontend → Backend → MinIO Flow:**

```mermaid
sequenceDiagram
    participant U as User
    participant PP as ProfilePage
    participant UPB as UserProfileBloc
    participant UR as UserRepository
    participant API as HttpApiService
    participant BE as Backend
    participant M as MinIO

    U->>PP: Select profile picture
    PP->>UPB: UpdateProfilePictureEvent
    UPB->>UR: updateProfilePicture()
    UR->>API: POST /api/v1/media/generate-upload-url
    API->>BE: Generate presigned URL
    BE->>M: Create presigned upload URL
    M-->>BE: Presigned URL (1 hour expiry)
    BE-->>API: {upload_url, file_id, expires_in}
    API->>M: PUT to presigned URL (direct upload)
    M-->>API: Upload success
    API->>BE: POST /api/v1/media/confirm-upload
    BE->>BE: Update user profile with file URL
    BE-->>API: Profile updated
    API-->>UR: Success
    UR-->>UPB: Updated user model
    UPB-->>PP: Profile picture updated
```

### **Key Components:**
- **DeferredProfilePictureService**: Handles offline uploads
- **MinIO Integration**: Direct client-to-storage uploads
- **Presigned URLs**: Secure, time-limited upload permissions
- **File Validation**: Content type and size restrictions

## 🔍 **3. Contact Search & Discovery Flow**

### **Frontend → Backend Search Flow:**

```mermaid
sequenceDiagram
    participant U as User
    participant CP as ContactsPage
    participant CB as ContactsBloc
    participant CR as ContactsRepository
    participant API as HttpApiService
    participant BE as Backend
    participant DB as PostgreSQL

    U->>CP: Enter search query
    CP->>CB: SearchContacts event
    CB->>CR: searchUsers()
    CR->>API: GET /api/v1/users/search?q=query
    API->>BE: User search request
    BE->>DB: Privacy-aware user search
    DB-->>BE: Filtered user results
    BE-->>API: Search results
    API-->>CR: List<UserContact>
    CR->>CR: Filter & merge with existing contacts
    CR-->>CB: Combined results
    CB-->>CP: Updated contact list
    CP->>U: Display search results
```

### **Privacy Controls:**
- **Backend**: Respects `is_private` user settings
- **Search Algorithm**: Name, username, email matching
- **Rate Limiting**: Prevents search abuse
- **Current User Filtering**: Excludes self from results

## 📞 **4. Contact Request Flow**

### **Send Contact Request:**

```mermaid
sequenceDiagram
    participant U as User
    participant CP as ContactsPage
    participant CRB as ContactRequestBloc
    participant CRR as ContactRequestRepository
    participant API as HttpApiService
    participant BE as Backend
    participant MQTT as EMQX
    participant R as Recipient

    U->>CP: Send contact request
    CP->>CRB: SendContactRequestEvent
    CRB->>CRR: sendContactRequest()
    CRR->>API: POST /api/v1/contact/requests
    API->>BE: Create contact request
    BE->>BE: Validate & create request
    BE->>MQTT: Publish to hopen/requests/{recipient_id}
    MQTT->>R: Real-time notification
    BE-->>API: Request created
    API-->>CRR: Success
    CRR-->>CRB: Request sent
    CRB-->>CP: UI updated
    CP->>U: Request sent confirmation
```

### **Accept/Decline Contact Request:**

```mermaid
sequenceDiagram
    participant R as Recipient
    participant NP as NotificationPage
    participant CRB as ContactRequestBloc
    participant CRR as ContactRequestRepository
    participant API as HttpApiService
    participant BE as Backend
    participant MQTT as EMQX
    participant S as Sender

    R->>NP: Accept/Decline request
    NP->>CRB: AcceptContactRequestEvent
    CRB->>CRR: acceptContactRequest()
    CRR->>API: POST /api/v1/contact/requests/{id}/accept
    API->>BE: Update request status
    BE->>BE: Create contact relationship
    BE->>MQTT: Notify sender of acceptance
    MQTT->>S: Real-time notification
    BE-->>API: Request processed
    API-->>CRR: Success
    CRR-->>CRB: Contact added
    CRB-->>NP: UI updated
    NP->>R: Contact added confirmation
```

## 📡 **5. Real-Time Communication (MQTT)**

### **MQTT Connection & Authentication:**

```mermaid
sequenceDiagram
    participant A as App
    participant MRS as MqttOnlyRealTimeService
    participant EMQX as EMQX Broker
    participant BE as Backend
    participant K as Kratos

    A->>MRS: Initialize MQTT
    MRS->>EMQX: Connect with credentials
    EMQX->>BE: POST /api/v1/auth/mqtt
    BE->>K: Validate session token
    K-->>BE: Session valid
    BE-->>EMQX: {"result": "allow"}
    EMQX-->>MRS: Connection established
    MRS->>EMQX: Subscribe to hopen/requests/{user_id}
    EMQX-->>MRS: Subscription confirmed
    MRS-->>A: Real-time service ready
```

### **Real-Time Message Flow:**
- **Topic Structure**: `hopen/requests/{user_id}` for notifications
- **QoS Level**: QoS 1 (at least once delivery)
- **Authentication**: JWT session token as MQTT password
- **Auto-Reconnect**: Handles network interruptions
- **Message Types**: Contact requests, bubble invites, friend requests

## 🫧 **6. Bubble Management Flow**

### **Create Bubble:**

```mermaid
sequenceDiagram
    participant U as User
    participant BP as BubblePage
    participant BB as BubbleBloc
    participant BR as BubbleRepository
    participant API as HttpApiService
    participant BE as Backend
    participant DB as PostgreSQL

    U->>BP: Create bubble
    BP->>BB: CreateBubbleEvent
    BB->>BR: createBubble()
    BR->>API: POST /api/v1/bubbles
    API->>BE: Create bubble request
    BE->>DB: ACID transaction
    DB-->>BE: Bubble created
    BE-->>API: Bubble response
    API-->>BR: ApiBubble
    BR-->>BB: BubbleEntity
    BB-->>BP: Bubble created
    BP->>U: Navigate to bubble
```

### **Join Bubble Request:**

```mermaid
sequenceDiagram
    participant U as User
    participant BP as BubblePage
    participant BJB as BubbleJoinRequestBloc
    participant BR as BubbleRepository
    participant API as HttpApiService
    participant BE as Backend
    participant MQTT as EMQX
    participant M as Members

    U->>BP: Request to join bubble
    BP->>BJB: SendJoinRequestEvent
    BJB->>BR: sendJoinRequest()
    BR->>API: POST /api/v1/bubbles/{id}/join-request
    API->>BE: Create join request
    BE->>BE: Validate & create request
    BE->>MQTT: Notify bubble members
    MQTT->>M: Real-time join request
    BE-->>API: Request created
    API-->>BR: Success
    BR-->>BJB: Request sent
    BJB-->>BP: UI updated
    BP->>U: Request sent confirmation
```

## 📊 **7. Data Synchronization & Caching**

### **Cache-First Repository Pattern:**
- **Local Cache**: Drift database for offline support
- **Remote Sync**: HTTP API for fresh data
- **Conflict Resolution**: Last-write-wins strategy
- **Background Sync**: Periodic data refresh

### **State Management:**
- **BLoC Pattern**: Reactive state management
- **Stream-based**: Real-time UI updates
- **Error Handling**: Comprehensive error states
- **Loading States**: Progressive loading indicators

## 🔒 **8. Security & Privacy**

### **Authentication Security:**
- **Ory Kratos**: Enterprise-grade identity management
- **Session Tokens**: Secure, time-limited tokens
- **HTTPS/HTTP3**: Encrypted communication
- **Certificate Pinning**: Development bypass available

### **Privacy Controls:**
- **User Settings**: Granular privacy preferences
- **Search Visibility**: Private profile protection
- **Contact Filtering**: Relationship-based access
- **Data Minimization**: Only necessary data collection

## 🚀 **9. Performance Optimizations**

### **Frontend Optimizations:**
- **HTTP/3 Support**: Faster network requests
- **Image Caching**: CachedNetworkImage for profile pictures
- **Lazy Loading**: Paginated data loading
- **State Persistence**: BLoC state restoration

### **Backend Optimizations:**
- **Connection Pooling**: Database connection management
- **Rate Limiting**: API abuse prevention
- **Caching**: Valkey for frequently accessed data
- **Microservices**: Independent scaling

## 📈 **10. Monitoring & Analytics**

### **Error Tracking:**
- **Comprehensive Logging**: Structured logging with Zap
- **Error Boundaries**: Flutter error handling
- **Health Checks**: Service availability monitoring
- **Performance Metrics**: Request timing and success rates

### **User Analytics:**
- **Event Tracking**: User interaction analytics
- **Privacy-Compliant**: Opt-in analytics collection
- **Real-time Insights**: Live user activity monitoring
- **A/B Testing**: Feature flag support

---

## 🎯 **Key Takeaways**

1. **Clean Architecture**: Clear separation of concerns across all layers
2. **Real-Time First**: MQTT integration for instant communication
3. **Security Focused**: Enterprise-grade authentication and privacy
4. **Scalable Design**: Microservices architecture for independent scaling
5. **Offline Support**: Cache-first approach with background synchronization
6. **Developer Experience**: Comprehensive tooling and documentation

This analysis demonstrates a well-architected system with robust end-to-end processes, strong security practices, and excellent user experience considerations.

## 📋 **11. Detailed API Endpoint Mapping**

### **Authentication Endpoints**
```
POST /api/v1/auth/register          # User registration (creates Kratos identity + local user)
POST /api/v1/auth/logout            # User logout (enterprise session invalidation)
GET  /api/v1/auth/profile           # Get user profile (requires Bearer token)
PUT  /api/v1/auth/profile           # Update user profile (requires Bearer token)
POST /api/v1/auth/mqtt              # MQTT authentication (EMQX hook)
POST /api/v1/auth/verify-email      # Email verification
POST /api/v1/auth/reset-password    # Password reset
POST /api/v1/auth/change-password   # Change password
```

### **User Management Endpoints**
```
GET  /api/v1/users/search           # Privacy-respecting user discovery
POST /api/v1/users/check-email      # Email availability validation
POST /api/v1/users/check-username   # Username availability validation
GET  /api/v1/users/:id              # Get user profile by ID
PUT  /api/v1/users/:id              # Update user profile (self only)
DELETE /api/v1/users/:id            # Delete user account
POST /api/v1/users/:id/ban          # Ban user (admin only)
POST /api/v1/users/:id/unban        # Unban user (admin only)
```

### **Contact Management Endpoints**
```
POST /api/v1/contact/requests                    # Send contact request
POST /api/v1/contact/requests/:id/accept         # Accept contact request
POST /api/v1/contact/requests/:id/decline        # Decline contact request
GET  /api/v1/contact/requests/:id               # Get contact request details
GET  /api/v1/contact/requests/history           # Get contact request history
POST /api/v1/contact/requests/expire-old        # Expire old requests
DELETE /api/v1/contact/requests/:id             # Cancel contact request
GET  /api/v1/contact/contacts                   # Get user's contacts
GET  /api/v1/contact/contacts/mutual            # Get mutual contacts
GET  /api/v1/contact/contacts/suggestions       # Get contact suggestions
GET  /api/v1/contact/requests/sent              # Get sent requests
GET  /api/v1/contact/requests/received          # Get received requests
DELETE /api/v1/contact/:contactId               # Remove contact
```

### **Media Upload Endpoints**
```
POST /api/v1/media/upload                       # Direct file upload
POST /api/v1/media/confirm-upload               # Confirm presigned upload
GET  /api/v1/media/:fileId                      # Get file (public for profile pictures)
HEAD /api/v1/media/:fileId                      # HEAD support for CachedNetworkImage
GET  /api/v1/media/:fileId/info                 # Get file metadata
DELETE /api/v1/media/:fileId                    # Delete file
GET  /api/v1/media/user/:userId                 # Get user's files
POST /api/v1/media/generate-upload-url          # Generate presigned upload URL
POST /api/v1/media/generate-download-url        # Generate presigned download URL
```

### **Bubble Management Endpoints**
```
GET  /api/v1/bubbles                            # Get user's bubbles
POST /api/v1/bubbles                            # Create new bubble
GET  /api/v1/bubbles/:id                        # Get bubble details
PUT  /api/v1/bubbles/:id                        # Update bubble
DELETE /api/v1/bubbles/:id                      # Delete bubble
POST /api/v1/bubbles/:id/join                   # Join bubble
POST /api/v1/bubbles/:id/leave                  # Leave bubble
POST /api/v1/bubbles/:id/start                  # Start bubble
POST /api/v1/bubbles/:id/invite                 # Invite to bubble
POST /api/v1/bubbles/:id/kick                   # Kick from bubble
GET  /api/v1/bubbles/:id/members                # Get bubble members
POST /api/v1/bubbles/:id/join-request           # Request to join bubble
POST /api/v1/bubbles/:id/invite-request         # Send invite request
```

## 🔄 **12. Data Flow Patterns**

### **Repository Pattern Implementation**
```dart
// Frontend Repository Pattern
abstract class ContactsRepository {
  Future<List<UserContact>> getContacts();
  Future<List<UserContact>> searchUsers(String query);
  Future<void> sendContactRequest(String recipientId, String? message);
  Future<void> acceptContactRequest(String requestId);
  Future<void> declineContactRequest(String requestId);
}

class ContactsRepositoryImpl implements ContactsRepository {
  final ContactsRemoteDataSource remoteDataSource;
  final HttpRemoteDataSource httpDataSource;

  // Cache-first implementation with fallback to remote
  @override
  Future<List<UserContact>> getContacts() async {
    try {
      // Try cache first, then remote
      final cachedContacts = await _getCachedContacts();
      if (cachedContacts.isNotEmpty) return cachedContacts;

      return await remoteDataSource.getContacts();
    } catch (e) {
      // Fallback to cached data on network error
      return await _getCachedContacts();
    }
  }
}
```

### **Backend Service Pattern**
```go
// Backend Service Pattern
type ContactService struct {
    logger      *zap.Logger
    repository  ContactRepository
    rateLimiter *ratelimit.RateLimiter
}

func (s *Service) sendContactRequest(c *gin.Context) {
    userID, _ := c.Get("user_id")

    // Rate limiting
    allowed, err := s.rateLimiter.AllowSocialOperation(
        c.Request.Context(),
        userID.(string),
        "contact_request",
    )
    if !allowed {
        c.JSON(http.StatusTooManyRequests, gin.H{
            "error": "Contact request rate limit exceeded",
        })
        return
    }

    // Business logic
    contact := &Contact{
        RequesterID: userID.(string),
        RecipientID: req.RecipientID,
        Status:      "pending",
        Message:     req.Message,
    }

    // Database operation
    if err := s.repository.CreateContact(c.Request.Context(), contact); err != nil {
        s.logger.Error("Failed to create contact request", zap.Error(err))
        c.JSON(http.StatusInternalServerError, gin.H{
            "error": "Failed to send contact request",
        })
        return
    }

    // Real-time notification via MQTT
    s.notifyContactRequest(contact)

    c.JSON(http.StatusCreated, contact)
}
```

## 🔐 **13. Security Implementation Details**

### **JWT Token Validation Flow**
```go
// Backend Auth Middleware
func AuthMiddleware(oryClient *ory.Client, logger *zap.Logger) gin.HandlerFunc {
    return func(c *gin.Context) {
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
            c.Abort()
            return
        }

        parts := strings.SplitN(authHeader, " ", 2)
        if len(parts) != 2 || parts[0] != "Bearer" {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header"})
            c.Abort()
            return
        }

        token := parts[1]

        // Validate with Ory Kratos
        session, err := oryClient.ValidateSession(c.Request.Context(), token)
        if err != nil || session == nil || !*session.Active {
            logger.Warn("Kratos session validation failed", zap.Error(err))
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or inactive session"})
            c.Abort()
            return
        }

        // Set user ID in context for downstream handlers
        c.Set("user_id", session.Identity.Id)
        c.Next()
    }
}
```

### **MQTT Authentication Integration**
```go
// MQTT Auth Endpoint for EMQX
func (s *Service) validateMQTTAuth(c *gin.Context) {
    var req struct {
        Username string `json:"username"` // User ID
        Password string `json:"password"` // JWT session token
        ClientID string `json:"clientid"`
        Topic    string `json:"topic,omitempty"`
        Action   string `json:"action,omitempty"`
    }

    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusOK, gin.H{
            "result": "deny",
            "reason": "invalid_request",
        })
        return
    }

    // Validate session token with Kratos
    session, err := s.oryClient.ValidateSession(ctx, req.Password)
    if err != nil {
        c.JSON(http.StatusOK, gin.H{
            "result": "deny",
            "reason": "invalid_token",
        })
        return
    }

    // Ensure username matches user ID from token
    if req.Username != session.Identity.Id {
        c.JSON(http.StatusOK, gin.H{
            "result": "deny",
            "reason": "username_mismatch",
        })
        return
    }

    c.JSON(http.StatusOK, gin.H{
        "result":  "allow",
        "user_id": session.Identity.Id,
    })
}
```

## 📱 **14. Frontend State Management Details**

### **BLoC Event/State Pattern**
```dart
// Contact Search BLoC Implementation
class ContactsBloc extends Bloc<ContactsEvent, ContactsState> {
  final ContactsRepository contactsRepository;
  final AuthBloc authBloc;

  ContactsBloc({
    required this.contactsRepository,
    required this.authBloc,
  }) : super(const ContactsState.initial()) {
    on<LoadContacts>(_onLoadContacts);
    on<SearchContacts>(_onSearchContacts);
    on<SendContactRequest>(_onSendContactRequest);
    on<AcceptContactRequest>(_onAcceptContactRequest);
  }

  Future<void> _onSearchContacts(
    SearchContacts event,
    Emitter<ContactsState> emit,
  ) async {
    final searchTerm = event.searchTerm.trim();

    if (searchTerm.isEmpty) {
      // Reset to local filtering
      final filteredContacts = _filterContacts(
        state.contacts,
        state.filterType,
        null,
      );
      emit(state.copyWith(filteredContacts: filteredContacts, searchQuery: null));
      return;
    }

    // Perform remote search
    final remoteResults = await contactsRepository.searchUsers(searchTerm);

    // Get current user ID to exclude self
    String? currentUserId;
    final authState = authBloc.state;
    if (authState.status == AuthStatus.authenticated) {
      currentUserId = authState.userId;
    }

    // Filter out current user
    final searchResultsWithoutSelf = currentUserId != null
        ? remoteResults.where((contact) => contact.id != currentUserId).toList()
        : remoteResults;

    // Apply sorting and filtering
    final sortedResults = _sortContacts(searchResultsWithoutSelf, state.sortOption);
    final filteredContacts = _filterContacts(
      sortedResults,
      state.filterType,
      searchTerm.toLowerCase(),
    );

    emit(state.copyWith(
      contacts: sortedResults,
      filteredContacts: filteredContacts,
      searchQuery: searchTerm.toLowerCase(),
    ));
  }
}
```

### **Real-Time Service Integration**
```dart
// MQTT Real-Time Service
class MqttOnlyRealTimeService {
  final MqttServerClient _client;
  final StreamController<RequestNotification> _requestController;

  Stream<RequestNotification> get requestStream => _requestController.stream;

  Future<void> initialize(String userId, String sessionToken) async {
    _client.server = 'emqx';
    _client.port = 1883;
    _client.clientIdentifier = 'flutter_${userId}_${DateTime.now().millisecondsSinceEpoch}';

    // Use session token as password for MQTT auth
    _client.username = userId;
    _client.password = sessionToken;

    _client.onConnected = _onConnected;
    _client.onDisconnected = _onDisconnected;
    _client.onSubscribed = _onSubscribed;

    await _client.connect();
  }

  void _onConnected() {
    print('🔗 MQTT Connected successfully');

    // Subscribe to user's request topic
    final topic = 'hopen/requests/${_client.username}';
    _client.subscribe(topic, MqttQos.atLeastOnce);

    // Listen for messages
    _client.updates!.listen((List<MqttReceivedMessage<MqttMessage>> messages) {
      for (final message in messages) {
        _handleMessage(message);
      }
    });
  }

  void _handleMessage(MqttReceivedMessage<MqttMessage> message) {
    final payload = MqttPublishPayload.bytesToString(
      (message.payload as MqttPublishMessage).payload.message,
    );

    if (payload != null && payload.isNotEmpty) {
      try {
        final data = jsonDecode(payload);
        final notification = RequestNotification.fromJson(data);
        _requestController.add(notification);
      } catch (e) {
        print('❌ Failed to parse MQTT message: $e');
      }
    }
  }
}
```

This comprehensive analysis demonstrates the sophisticated architecture and robust implementation patterns used throughout the Hopen application, ensuring scalable, secure, and maintainable code across both frontend and backend systems.
